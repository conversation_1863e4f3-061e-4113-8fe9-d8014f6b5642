# siift - Modern Project Management Platform

A modern project management platform built with Next.js 15, TypeScript, and shadcn/ui components.

## 🚀 Quick Start

1. **Install dependencies:**

   ```bash
   npm install
   ```

2. **Set up environment:**

   ```bash
   cp .env.example .env.local
   # Add your Clerk and other API keys
   ```

3. **Start development:**

   ```bash
   npm run dev
   ```

4. **Open [http://localhost:3001](http://localhost:3001)**

## � Upgrading Next.js

To upgrade to the latest Next.js version:

```bash
# Check current version
npm list next

# Upgrade to latest stable
npm install next@latest

# Or upgrade to specific version
npm install next@15.4.4
```

## �📁 App Structure

```
src/
├── app/                    # Next.js App Router
│   ├── auth/              # Authentication pages (login, register)
│   ├── admin/             # Admin panel & analytics
│   ├── user-dashboard/    # User dashboard
│   ├── projects/          # Project management
│   ├── profile/           # User profile
│   └── api/               # API routes
├── components/            # UI Components
│   ├── ui/               # shadcn/ui base components
│   ├── auth/             # Authentication components
│   ├── landing/          # Landing page components
│   ├── layout/           # Headers, footers, layouts
│   └── navigation/       # Navigation components
├── lib/                  # Utilities & API clients
├── hooks/                # Custom React hooks
├── stores/               # Zustand state management
├── types/                # TypeScript definitions
└── contexts/             # React contexts
```

## ✨ Features

- **� Authentication** - Clerk integration with social login
- **📊 Admin Panel** - Analytics dashboard with charts & metrics
- **👤 User Dashboard** - Personal project management
- **📁 Project Management** - Create, organize, and manage projects
- **🎨 Modern UI** - shadcn/ui components with dark/light themes
- **📱 Responsive** - Mobile-first design
- **🚀 Fast** - Next.js 15 with App Router & Turbopack

## 🛠 Tech Stack

- **Frontend:** Next.js 15, TypeScript, Tailwind CSS
- **UI:** shadcn/ui components, Lucide icons
- **Auth:** Clerk (Google, Apple, email)
- **State:** Zustand stores
- **Analytics:** PostHog
- **Deploy:** Docker, Railway

## 🔧 Key Environment Variables

```bash
# Clerk Authentication
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_...
CLERK_SECRET_KEY=sk_test_...

# API Configuration
NEXT_PUBLIC_ADMIN_API_URL=http://localhost:3000
JWT_SECRET=your-secret-key

# Analytics (optional)
NEXT_PUBLIC_POSTHOG_KEY=phc_...
```

## 🏗 Available Scripts

```bash
# Development
npm run dev              # Start dev server (port 3001)
npm run build           # Build for production
npm run build:skip-all  # Build bypassing lint/type errors
npm run lint            # Run ESLint
npm run lint:fix        # Fix ESLint errors

# Docker
npm run docker:build    # Build Docker image
npm run docker:run      # Run Docker container
```

## 🐳 Docker Deployment

```bash
# Quick start with Docker Compose
docker-compose up --build

# Production deployment
docker-compose -f docker-compose.prod.yml up --build

# Manual build
./docker-build.sh
```

## 📂 Key Pages

- **Landing:** `/` - Marketing page with features
- **Auth:** `/auth/login`, `/auth/register` - Authentication
- **Dashboard:** `/user-dashboard` - User home page
- **Projects:** `/projects` - Project management
- **Admin:** `/admin` - Analytics & admin panel
- **Profile:** `/profile` - User settings

## 🤝 Contributing

1. Fork the repo
2. Create a feature branch
3. Make your changes
4. Submit a pull request

---

Built with ❤️ using Next.js 15 and TypeScript

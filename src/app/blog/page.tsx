import { BlogCard } from "@/components/blog/blog-card";
import { Footer } from "@/components/layout/footer";
import { Header } from "@/components/layout/header";
import { generateSEOMetadata } from "@/components/seo/SEOHead";
import {
  StructuredData,
  structuredDataSchemas,
} from "@/components/seo/StructuredData";
import { getAllPosts } from "@/data/blog-posts";

export const metadata = generateSEOMetadata({
  title: "Blog - Latest Insights & Updates",
  description:
    "Stay informed with our latest articles on project management, productivity, team collaboration, and industry trends. Expert insights to help you succeed.",
  keywords: [
    "blog",
    "project management",
    "productivity",
    "team collaboration",
    "business insights",
    "industry trends",
  ],
  url: "/blog",
  type: "website",
});

export default function BlogPage() {
  const posts = getAllPosts();

  // Blog listing structured data
  const blogStructuredData = structuredDataSchemas.blog({
    name: "siift Blog",
    description:
      "Latest insights on project management, productivity, and team collaboration",
    url: "https://siift.app/blog",
    posts: posts.map((post) => ({
      title: post.title,
      description: post.description,
      url: `https://siift.app/blog/${post.slug}`,
      publishedTime: post.publishedAt,
      author: "siift Team",
      image: post.image || "https://siift.app/images/og-image.png",
    })),
  });

  return (
    <>
      <StructuredData data={blogStructuredData} />
      <div className="min-h-screen flex flex-col bg-background">
        <Header />
        <main className="flex-1">
          <div className="container mx-auto px-4 py-12">
            {/* Hero Section */}
            <div className="text-center mb-16">
              <h1 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
                Our Blog
              </h1>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Discover the latest insights, tips, and trends in project
                management, productivity, and team collaboration. Learn from
                industry experts and take your projects to the next level.
              </p>
            </div>

            {/* Blog Posts Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {posts.map((post) => (
                <BlogCard key={post.id} post={post} />
              ))}
            </div>

            {/* Empty State */}
            {posts.length === 0 && (
              <div className="text-center py-16">
                <h3 className="text-2xl font-semibold text-foreground mb-4">
                  No posts found
                </h3>
                <p className="text-muted-foreground">
                  Check back soon for new content!
                </p>
              </div>
            )}
          </div>
        </main>
        <Footer />
      </div>
    </>
  );
}

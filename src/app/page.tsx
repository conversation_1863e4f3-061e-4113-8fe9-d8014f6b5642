import { LandingPage } from "@/components/landing/landing-page";
import { generateSEOMetadata } from "@/components/seo/SEOHead";
import {
  StructuredData,
  structuredDataSchemas,
} from "@/components/seo/StructuredData";

export const metadata = generateSEOMetadata({
  title: "siift - Modern Project Management Platform",
  description:
    "Streamline your workflow with siift's powerful project management tools. Built for teams who value productivity, collaboration, and intuitive design.",
  keywords: [
    "project management",
    "productivity",
    "team collaboration",
    "workflow",
    "task management",
    "project planning",
  ],
  url: "/",
  type: "website",
});

export default function Home() {
  // Software Application structured data for the homepage
  const softwareAppData = structuredDataSchemas.softwareApplication({
    name: "siift",
    description:
      "A modern project management platform built with Next.js and NestJS",
    url: "https://siift.app",
    category: "BusinessApplication",
    operatingSystem: "Web Browser",
    price: "0",
    priceCurrency: "USD",
  });

  return (
    <>
      <StructuredData data={softwareAppData} />
      <LandingPage />
    </>
  );
}

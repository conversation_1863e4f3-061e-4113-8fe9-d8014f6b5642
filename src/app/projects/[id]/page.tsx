"use client";

import { <PERSON><PERSON><PERSON>er, ProjectMainContent } from "@/components/project";
import { ProjectSidebar } from "@/components/project-sidebar";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { useParams, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { useResizable } from "../../../hooks/useResizable";
// import { fetchBusinessSections } from "../../../lib/businessSectionsData";
import { useBusinessSections } from "@/hooks/queries/useBusinessSections";
import { useProject } from "@/hooks/queries/useProjects";
import { useInvalidateSiift } from "@/hooks/queries/useSiift";
import {
  useAllProjectTopics,
  useAllTopicEntries,
} from "@/hooks/queries/useTopics";
import { useAiIntake } from "@/hooks/useAiIntake";
import { usePromptStore } from "@/stores/promptStore";
import { useBusinessItemStore } from "../../../stores/businessItemStore";
import { useBusinessSectionStore } from "../../../stores/businessSectionStore";

// Remove mocks for drafts/files; these sections will be hidden until real data exists
const mockDraftItems: any[] = [];
const mockFileItems: any[] = [];

export default function ProjectDetailPage() {
  const params = useParams();
  const projectId = params.id;
  const [activeContent, setActiveContent] = useState<"drafts" | "files" | null>(
    null
  );
  const [chatWidth, setChatWidth] = useState<"45%" | "45%">("45%");
  const [isChatCollapsed, setIsChatCollapsed] = useState(false);
  const [sidebarWidth, setSidebarWidth] = useState("45vw");

  // Sync sidebar width with chat width
  const handleChatWidthChange = (width: "45%" | "45%") => {
    setChatWidth(width);
    setSidebarWidth(width === "45%" ? "45vw" : "45vw");
  };

  const {
    sections,
    isLoading,
    error,
    setSections,
    setLoading,
    setError,
    setTopicEntries,
  } = useBusinessSectionStore();
  const { selectedItem, setSelectedItem } = useBusinessItemStore();
  const { stage, resumeIntake, startIntake } = useAiIntake();
  const searchParams = useSearchParams();
  const { getPrompt, removePrompt } = usePromptStore();
  const invalidateSiift = useInvalidateSiift();

  // Dynamic sidebar width based on selected item
  const currentSidebarWidth = selectedItem ? "30vw" : sidebarWidth;

  // Reset to default view on page load/refresh
  useEffect(() => {
    setSelectedItem(null);
  }, []);

  // Resume AI intake stream; if promptId provided, use it to start intake with that prompt
  useEffect(() => {
    if (!projectId) return;
    const pid = projectId as string;
    const promptId = searchParams?.get("promptId");
    if (promptId) {
      const prompt = getPrompt(promptId);
      if (prompt && prompt.trim()) {
        console.log(
          "[ProjectDetailPage] Starting intake with routed promptId",
          { promptId }
        );
        void startIntake(pid, prompt.trim());
        removePrompt(promptId);
        return;
      }
    }
    // Fallback: just resume existing stream if any
    resumeIntake(pid);
  }, [
    projectId,
    searchParams,
    getPrompt,
    removePrompt,
    startIntake,
    resumeIntake,
  ]);

  // Load business sections via TanStack Query
  const {
    data: queriedSections = [],
    isLoading: sectionsLoading,
    error: sectionsError,
  } = useBusinessSections(projectId as string);

  // Load project meta (title)
  const { data: projectMeta, isLoading: projectMetaLoading } = useProject(
    projectId as string
  );

  // Helper function to get project title with loading state
  const getProjectTitle = () => {
    if (projectMetaLoading) return "Loading project...";
    return (projectMeta as any)?.name || `Project ${projectId}`;
  };

  // Fetch all topics grouped by subjects, normalized to section IDs
  // NOTE: This MUST be declared before any effects that reference `allTopics`
  const { data: allTopics = [], isLoading: topicsLoading } =
    useAllProjectTopics(projectId as string);

  // Debug store state
  const { sections: storeSections } = useBusinessSectionStore();
  console.log("🔥 [ProjectDetailPage] Store sections:", {
    storeSectionsCount: storeSections?.length || 0,
    storeSections: storeSections?.map((s) => ({
      id: s.id,
      title: s.title,
      itemsCount: s.items?.length || 0,
      itemsWithEntries:
        s.items?.filter(
          (item) =>
            Array.isArray((item as any)?.entries) &&
            (item as any).entries.length > 0
        ).length || 0,
    })),
  });

  useEffect(() => {
    console.log("[ProjectDetailPage] business-sections effect", {
      sectionsLoading,
      sectionsError: sectionsError?.message,
      queriedCount: Array.isArray(queriedSections)
        ? queriedSections.length
        : "n/a",
    });
    // Mirror react-query state into the zustand store ONLY when values actually change
    if (isLoading !== sectionsLoading) {
      setLoading(sectionsLoading);
    }

    const nextError = sectionsError ? sectionsError.message : null;
    if (error !== nextError) {
      setError(nextError);
    }

    if (Array.isArray(queriedSections) && queriedSections.length) {
      // Populate sections with topics as items
      const sectionsWithTopics = queriedSections.map((section) => {
        // Find topics for this section
        const sectionTopics = allTopics.filter(
          (t) => (t.mappedCategoryId ?? t.sectionId) === section.id
        );

        // Convert topics to items
        const prev = storeSections.find((s) => s.id === section.id);
        const prevItems = prev?.items || [];
        const topicItems = sectionTopics.map((t) => {
          const id = String(t.numericTopicId);
          // Preserve existing entries if present in store
          const prevItem = prevItems.find((i) => i.id === id) as any;
          const preservedEntries = Array.isArray(prevItem?.entries)
            ? prevItem.entries
            : [];
          return {
            id,
            title: t.mappedLabel || t.title,
            status: "idea" as const,
            actions: 0,
            ideas: 0,
            results: 0,
            icon: "Lightbulb" as any,
            entries: preservedEntries,
            sectionId: section.id,
          };
        });

        return {
          ...section,
          items: topicItems,
        };
      });

      // Avoid re-setting identical sections arrays to prevent render cascades
      const sectionsChanged =
        !Array.isArray(storeSections) ||
        storeSections.length !== sectionsWithTopics.length ||
        // compare only ids and item counts to avoid clobbering entries
        JSON.stringify(
          storeSections.map((s) => ({
            id: s.id,
            items: s.items?.map((i) => i.id) || [],
          }))
        ) !==
          JSON.stringify(
            sectionsWithTopics.map((s) => ({
              id: s.id,
              items: s.items?.map((i) => i.id) || [],
            }))
          );
      if (sectionsChanged) {
        console.log(
          "[ProjectDetailPage] setSections with topics ->",
          sectionsWithTopics.length,
          "sections with total items:",
          sectionsWithTopics.reduce((sum, s) => sum + (s.items?.length || 0), 0)
        );
        setSections(sectionsWithTopics);
      }
    }
  }, [sectionsLoading, sectionsError, queriedSections, allTopics]);

  // Handle business item selection - show detail view
  const handleBusinessItemClick = (item: any) => {
    console.log("🔥 [ProjectDetailPage] handleBusinessItemClick:", {
      itemId: item.id,
      itemTitle: item.title,
      itemSectionId: item.sectionId,
      entriesCount: item.entries?.length || 0,
      hasEntries: !!item.entries,
    });
    setSelectedItem(item);
  };

  // Debug logging
  console.log("[ProjectDetailPage] DEBUG:", {
    topicsLoading,
    projectId,
    allTopicsCount: allTopics.length,
    sample: allTopics.slice(0, 3),
  });

  // Fetch all topic entries at once
  const { data: allTopicEntries = {}, isLoading: topicEntriesLoading } =
    useAllTopicEntries(projectId as string, allTopics);

  // Update the store with the fetched topic entries
  useEffect(() => {
    console.log("🔥 [ProjectDetailPage] allTopicEntries effect:", {
      allTopicEntriesKeys: Object.keys(allTopicEntries),
      allTopicEntriesLength: Object.keys(allTopicEntries).length,
      allTopicsCount: allTopics.length,
      allTopics: allTopics.slice(0, 3), // First 3 topics
      sampleEntries: Object.entries(allTopicEntries)
        .slice(0, 3)
        .map(([topicId, entries]) => ({
          topicId,
          entriesCount: entries.length,
          firstEntry: entries[0],
        })),
    });

    if (Object.keys(allTopicEntries).length > 0) {
      console.log("🔥 [ProjectDetailPage] Setting topic entries in store...");
      allTopics.forEach((topic) => {
        const entries = allTopicEntries[topic.topicId] || [];
        const mappedEntries = entries.map((e) => ({
          id: String(e.id),
          title: e.idea || "",
          actions: e.action || "",
          result: e.result || "",
          status: (e.status as any) || "idea",
        }));
        const effectiveSectionId = topic.mappedCategoryId ?? topic.sectionId;
        console.log(
          `🔥 [ProjectDetailPage] Setting entries for ${effectiveSectionId}/${topic.topicId}:`,
          {
            entriesCount: mappedEntries.length,
            mappedEntries: mappedEntries.slice(0, 2),
          }
        );
        setTopicEntries(effectiveSectionId, topic.topicId, mappedEntries);
      });
    }
  }, [allTopicEntries, allTopics, setTopicEntries]);

  // Get the current item details from the selected item's entries
  const itemDetails =
    selectedItem && (selectedItem as any).entries
      ? (selectedItem as any).entries
      : [];
  // Compute project progress based on confirmed dependency topics
  const progress = (() => {
    try {
      if (!Array.isArray(allTopics) || allTopics.length === 0) return 0;
      // A topic is considered complete if it has at least one confirmed entry
      let completed = 0;
      allTopics.forEach((t) => {
        const sectionKey = t.mappedCategoryId ?? t.sectionId;
        const entries = (allTopicEntries?.[t.topicId] ?? []).length
          ? allTopicEntries?.[t.topicId]
          : (
              sections
                .find((s) => s.id === sectionKey)
                ?.items.find((i) => i.id === String(t.numericTopicId)) as any
            )?.entries ?? [];
        const isConfirmed =
          Array.isArray(entries) &&
          entries.some((e: any) => e.status === "confirmed");
        if (isConfirmed) completed += 1;
      });
      return Math.round((completed / allTopics.length) * 100);
    } catch (_e) {
      return 0;
    }
  })();

  console.log("🔥 [ProjectDetailPage] itemDetails for selected item:", {
    selectedItemId: selectedItem?.id,
    selectedItemTitle: selectedItem?.title,
    selectedItemSectionId: (selectedItem as any)?.sectionId,
    itemDetailsCount: itemDetails.length,
    itemDetails: itemDetails.slice(0, 2),
  });

  // Handle back to items
  const handleBackToItems = () => {
    setSelectedItem(null);
  };

  // Resize functionality
  const resizable = useResizable({
    initialWidth: sidebarWidth,
    minWidthPercent: 10,
    maxWidthPercent: 70,
    onWidthChange: (width) => {
      // Convert percentage to viewport width
      const widthPercent = parseFloat(width.replace("%", ""));
      const vwWidth = `${widthPercent}vw`;
      setSidebarWidth(vwWidth);

      // Update chatWidth to match sidebarWidth for consistency
      if (widthPercent <= 70) {
        setChatWidth("45%");
      } else {
        setChatWidth("45%");
      }
    },
    onCollapse: () => {
      setIsChatCollapsed(true);
    },
  });

  // Invalidate SIIFT queries when ingestion completes
  useEffect(() => {
    if (stage === "ready") {
      console.debug("[ProjectDetailPage] SIIFT ready → invalidating queries", {
        projectId,
      });
      invalidateSiift(projectId as string);
    }
  }, [stage, projectId, invalidateSiift]);

  return (
    <div className="h-screen overflow-hidden">
      <SidebarProvider
        defaultOpen={true}
        style={
          {
            "--sidebar-width": currentSidebarWidth,
            "--sidebar-width-mobile": "18rem",
            "--sidebar-width-icon": "5rem",
            transition: "all 0.3s ease-in-out",
          } as React.CSSProperties
        }
      >
        <ProjectSidebar
          projectId={projectId as string}
          // show the human title above chat; fallback to projectId
          chatWidth={chatWidth}
          setChatWidth={handleChatWidthChange}
          isChatCollapsed={isChatCollapsed}
          setIsChatCollapsed={setIsChatCollapsed}
          selectedBusinessItem={selectedItem}
          showDescription={!!selectedItem}
          onBackToProject={handleBackToItems}
          resizeHandle={{
            onMouseDown: resizable.handleMouseDown,
            isDragging: resizable.isDragging,
          }}
          projectTitle={getProjectTitle()}
        />
        <SidebarInset className="flex-1 flex flex-col h-screen overflow-hidden">
          <ProjectHeader
            activeContent={activeContent}
            setActiveContent={setActiveContent}
            selectedBusinessItem={selectedItem}
            onBackToItems={handleBackToItems}
            progress={progress}
            projectTitle={getProjectTitle()}
            projectName={getProjectTitle()}
          />

          <ProjectMainContent
            activeContent={activeContent}
            setActiveContent={setActiveContent}
            mockDraftItems={mockDraftItems}
            mockFileItems={mockFileItems}
            selectedItem={selectedItem}
            itemDetails={itemDetails}
            sections={sections}
            isLoading={isLoading}
            error={error}
            onBusinessItemClick={handleBusinessItemClick}
            onBackToItems={handleBackToItems}
            projectId={projectId as string}
            stage={stage}
            allTopics={allTopics}
            entriesByTopic={allTopicEntries}
            isDependencyLoading={topicEntriesLoading}
          />

          {/* AI Intake Controls removed per spec; page only listens to backend events */}

          {/* Bottom fade effect to indicate more content - fixed at bottom of viewport */}
          <div
            className="fixed bottom-0 h-16 bg-gradient-to-t from-gray-50 via-gray-50/80 to-transparent dark:from-gray-900 dark:via-gray-900/80 dark:to-transparent pointer-events-none z-10"
            style={{
              left: `var(--sidebar-width, 45vw)`,
              right: "0",
            }}
          />
          <div
            className="fixed bottom-0 h-8 backdrop-blur-sm pointer-events-none z-10"
            style={{
              left: `var(--sidebar-width, 45vw)`,
              right: "0",
            }}
          />
        </SidebarInset>
      </SidebarProvider>
    </div>
  );
}

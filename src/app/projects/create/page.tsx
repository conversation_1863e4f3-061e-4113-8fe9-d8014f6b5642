"use client";

import { ProjectCreationAnimation } from "@/components/project-creation/ProjectCreationAnimation";
import { useProjectCreationStore } from "@/stores/projectCreationStore";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export default function ProjectCreatePage() {
  const router = useRouter();
  const { currentStep, reset } = useProjectCreationStore();
  const [hasCheckedInitialState, setHasCheckedInitialState] = useState(false);

  // Give the store a moment to hydrate before checking if we should redirect
  useEffect(() => {
    const timer = setTimeout(() => {
      setHasCheckedInitialState(true);
    }, 100); // Small delay to allow store to hydrate

    return () => clearTimeout(timer);
  }, []);

  // Redirect if not in creation flow, but only after we've given store time to hydrate
  useEffect(() => {
    if (hasCheckedInitialState && currentStep === "idle") {
      console.log("🔄 Redirecting to dashboard - currentStep is idle");
      router.push("/user-dashboard");
    }
  }, [currentStep, router, hasCheckedInitialState]);

  // Show loading while we check initial state
  if (!hasCheckedInitialState) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return <ProjectCreationAnimation />;
}

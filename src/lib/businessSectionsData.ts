import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Lightbulb,
  BarChart3,
  TrendingUp,
  Star,
  Package,
  Settings,
  Target,
  Megaphone,
  Palette,
  MessageCircle,
  FileText,
  Building,
  DollarSign,
  Shield,
  Heart,
} from "lucide-react";
import type { BusinessSection } from "@/types/BusinessSection.types";

export const mockBusinessSections: BusinessSection[] = [
  {
    id: "market",
    title: "Market",
    items: [
      {
        id: "problem",
        title: "Problem",
        status: "idea",
        actions: 2,
        ideas: 3,
        results: 1,
        icon: AlertTriangle,
      },
      {
        id: "audience",
        title: "Audience",
        status: "action",
        actions: 1,
        ideas: 2,
        results: 1,
        icon: Users,
      },
      {
        id: "alternatives",
        title: "Alternatives",
        status: "idea",
        actions: 0,
        ideas: 1,
        results: 0,
        icon: Lightbulb,
      },
      {
        id: "market-size",
        title: "Market Size",
        status: "idea",
        actions: 0,
        ideas: 1,
        results: 0,
        icon: BarChart3,
      },
      {
        id: "trends",
        title: "Trends",
        status: "idea",
        actions: 0,
        ideas: 1,
        results: 0,
        icon: TrendingUp,
      },
    ],
  },
  {
    id: "solution",
    title: "Solution",
    items: [
      {
        id: "uvp",
        title: "Unique Value Proposition",
        status: "idea",
        actions: 0,
        ideas: 1,
        results: 0,
        icon: Star,
      },
      {
        id: "product",
        title: "Product",
        status: "idea",
        actions: 0,
        ideas: 1,
        results: 0,
        icon: Package,
      },
      {
        id: "tech",
        title: "Tech",
        status: "idea",
        actions: 0,
        ideas: 1,
        results: 0,
        icon: Settings,
      },
      {
        id: "packages",
        title: "Packages",
        status: "idea",
        actions: 0,
        ideas: 0,
        results: 0,
        icon: Package,
      },
    ],
  },
  {
    id: "sales-marketing",
    title: "Sales & Marketing",
    items: [
      {
        id: "positioning",
        title: "Positioning",
        status: "idea",
        actions: 0,
        ideas: 1,
        results: 0,
        icon: Target,
      },
      {
        id: "channels",
        title: "Channels",
        status: "idea",
        actions: 0,
        ideas: 1,
        results: 0,
        icon: Megaphone,
      },
      {
        id: "brand",
        title: "Brand",
        status: "idea",
        actions: 0,
        ideas: 1,
        results: 0,
        icon: Palette,
      },
      {
        id: "messaging",
        title: "Messaging",
        status: "idea",
        actions: 0,
        ideas: 1,
        results: 0,
        icon: MessageCircle,
      },
      {
        id: "assets",
        title: "Assets",
        status: "idea",
        actions: 0,
        ideas: 0,
        results: 0,
        icon: FileText,
      },
      {
        id: "sales-motion",
        title: "Sales Motion",
        status: "idea",
        actions: 0,
        ideas: 0,
        results: 0,
        icon: TrendingUp,
      },
      {
        id: "metrics",
        title: "Metrics",
        status: "idea",
        actions: 0,
        ideas: 1,
        results: 0,
        icon: BarChart3,
      },
    ],
  },
  {
    id: "company",
    title: "Company",
    items: [
      {
        id: "why",
        title: "Why",
        status: "idea",
        actions: 0,
        ideas: 0,
        results: 0,
        icon: Heart,
      },
      {
        id: "advantages",
        title: "Advantages",
        status: "idea",
        actions: 0,
        ideas: 0,
        results: 0,
        icon: Star,
      },
      {
        id: "business-model",
        title: "Business Model",
        status: "idea",
        actions: 0,
        ideas: 0,
        results: 0,
        icon: Building,
      },
      {
        id: "cost",
        title: "Cost",
        status: "action",
        actions: 0,
        ideas: 0,
        results: 0,
        icon: DollarSign,
      },
      {
        id: "risks",
        title: "Risks",
        status: "idea",
        actions: 0,
        ideas: 0,
        results: 0,
        icon: Shield,
      },
      {
        id: "revenue",
        title: "Revenue",
        status: "idea",
        actions: 0,
        ideas: 0,
        results: 0,
        icon: DollarSign,
      },
      {
        id: "team",
        title: "Team",
        status: "unproven",
        actions: 0,
        ideas: 0,
        results: 0,
        icon: Users,
      },
    ],
  },
];

// API function to fetch business sections (placeholder for future implementation)
export const fetchBusinessSections = async (
  projectId: string
): Promise<BusinessSection[]> => {
  // This will be replaced with actual API call
  // For now, return mock data
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockBusinessSections);
    }, 500);
  });
};

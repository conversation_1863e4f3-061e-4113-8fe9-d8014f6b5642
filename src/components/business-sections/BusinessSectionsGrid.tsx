"use client";

import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import type {
  BusinessItem,
  BusinessSection,
} from "@/types/BusinessSection.types";
import {
  AlertTriangle,
  BarChart3,
  Building,
  Check,
  ChevronUp,
  DollarSign,
  FileText,
  Heart,
  Lightbulb,
  Megaphone,
  MessageCircle,
  Package,
  Palette,
  Settings,
  Shield,
  Star,
  Target,
  TrendingUp,
  Users,
  Zap,
} from "lucide-react";
import { useMemo, useState } from "react";

import type {
  ProjectTopicSummary,
  TopicEntry,
} from "@/hooks/queries/useTopics";
import { useBusinessSectionStore } from "@/stores/businessSectionStore";

// Topic icon mapping based on topic titles
const getTopicIcon = (title: string) => {
  const titleLower = title.toLowerCase();
  if (titleLower.includes("problem")) return AlertTriangle;
  if (titleLower.includes("audience") || titleLower.includes("customer"))
    return Users;
  if (titleLower.includes("alternative") || titleLower.includes("competitor"))
    return Lightbulb;
  if (titleLower.includes("market") || titleLower.includes("size"))
    return BarChart3;
  if (titleLower.includes("trend")) return TrendingUp;
  if (titleLower.includes("value") || titleLower.includes("uvp")) return Star;
  if (titleLower.includes("product") || titleLower.includes("feature"))
    return Package;
  if (titleLower.includes("tech") || titleLower.includes("technology"))
    return Settings;
  if (titleLower.includes("positioning")) return Target;
  if (titleLower.includes("marketing") || titleLower.includes("campaign"))
    return Megaphone;
  if (titleLower.includes("brand") || titleLower.includes("design"))
    return Palette;
  if (titleLower.includes("communication") || titleLower.includes("message"))
    return MessageCircle;
  if (titleLower.includes("content") || titleLower.includes("documentation"))
    return FileText;
  if (titleLower.includes("structure") || titleLower.includes("organization"))
    return Building;
  if (
    titleLower.includes("revenue") ||
    titleLower.includes("monetization") ||
    titleLower.includes("pricing")
  )
    return DollarSign;
  if (titleLower.includes("legal") || titleLower.includes("compliance"))
    return Shield;
  if (titleLower.includes("culture") || titleLower.includes("values"))
    return Heart;
  return Lightbulb; // Default fallback
};

// Item Row Component
const ItemRow = ({
  item,
  onItemClick,
  disabled = false,
}: {
  item: BusinessItem & { entries?: any[] };
  onItemClick: (item: BusinessItem) => void;
  disabled?: boolean;
  subtitle?: string;
  lockedDeps?: string[];
}) => {
  const entries = (item as any).entries || [];

  // Calculate counts from actual entries
  const ideaCount = entries.filter((e: any) => e.status === "idea").length;
  const actionCount = entries.filter((e: any) => e.status === "action").length;
  const resultCount = entries.filter(
    (e: any) => e.status === "confirmed"
  ).length;

  const getStatusStyles = (status: string, disabled: boolean = false) => {
    if (disabled) {
      return "bg-gray-50 dark:bg-gray-800 text-gray-400 dark:text-gray-500 opacity-60";
    }

    switch (status) {
      // case "validated":
      //   return "bg-green-50 dark:bg-green-900/20 text-green-900 dark:text-green-100 border-green-200 dark:border-green-700";
      // case "action":
      //   return "bg-blue-50 dark:bg-blue-900/20 text-blue-900 dark:text-blue-100 border-blue-200 dark:border-blue-700";
      // case "idea":
      //   return "bg-yellow-50 dark:bg-yellow-900/20 text-yellow-900 dark:text-yellow-100 border-yellow-200 dark:border-yellow-700";
      // case "invalidated":
      //   return "bg-gray-50 dark:bg-gray-800 text-gray-600 dark:text-gray-300 border-gray-200 dark:border-gray-600";
      default:
        return "bg-gray-100 dark:bg-background text-gray-600 dark:text-gray-300";
    }
  };
  // Note: left-side topic icon uses neutral color; right-side status icons use semantic colors

  const content = (
    <div
      className={`flex items-center justify-between p-3 rounded-lg mb-3 transition-all cursor-pointer hover:shadow-md hover:scale-[1.02] ${getStatusStyles(
        item.status,
        disabled ?? false
      )} hover:bg-primary/10 dark:hover:bg-primary/20 border relative z-40 ${
        disabled ? "cursor-not-allowed opacity-50 group" : ""
      }`}
      onClick={() => {
        if (disabled) return;
        console.log(`Clicked on ${item.title} with ${entries.length} entries`);
        onItemClick(item);
      }}
    >
      <div className="flex items-start gap-3">
        <span className="font-medium text-sm">{item.title}</span>
      </div>
      {/* Hover text overlay for blocked items */}
      {disabled && (
        <div className="absolute inset-0 bg-gray-500/60  dark:bg-gray-800/60 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-10 ">
          <span className="text-white text-sm font-bold">
            It's too early to work on this
          </span>
        </div>
      )}
      <div
        className={`flex items-center gap-1 flex-shrink-0 ${
          disabled ? "opacity-50" : ""
        }`}
      >
        {entries.length > 0 && (
          <>
            {ideaCount > 0 && (
              <Badge
                variant="secondary"
                className="bg-yellow-50 dark:bg-yellow-500 text-black dark:text-white text-xs px-1.5 py-0.5 border border-yellow-300 dark:border-yellow-700 h-6"
              >
                <Lightbulb className="h-3 w-3" />
                {ideaCount > 1 ? ideaCount : ""}
              </Badge>
            )}
            {actionCount > 0 && (
              <Badge
                variant="secondary"
                className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs px-1.5 border border-blue-200 dark:border-blue-700 h-6"
              >
                <Zap className="h-3 w-3" />
                {actionCount > 1 ? actionCount : ""}
              </Badge>
            )}
            {resultCount > 0 && (
              <Badge
                variant="secondary"
                className="bg-green-100 dark:bg-green-900 text-green-900 dark:text-green-200 text-xs px-1.5 border border-green-700 dark:border-green-600 h-6"
              >
                <Check className="h-4 w-4" />
                {resultCount > 1 ? resultCount : ""}
              </Badge>
            )}
          </>
        )}
      </div>
    </div>
  );

  return content;
};

// Expandable Card Component
const ExpandableCard = ({
  section,
  onItemClick,
  stage,
}: {
  section: BusinessSection & { isUnlocked?: boolean };
  onItemClick: (item: BusinessItem) => void;
  projectId: string;
  stage: string;
}) => {
  const [isExpanded, setIsExpanded] = useState(true);

  // Determine if any topic in this section already has entries in the store
  const hasAnyEntries =
    Array.isArray(section.items) &&
    section.items.some(
      (it) =>
        Array.isArray((it as any)?.entries) && (it as any).entries.length > 0
    );

  console.log(`🔍 [ExpandableCard] ${section.title}:`, {
    sectionId: section.id,
    stage,
    itemsCount: section.items?.length || 0,
    hasAnyEntries,
    items: section.items?.map((item) => ({
      id: item.id,
      title: item.title,
      hasEntries: Array.isArray((item as any)?.entries),
      entriesCount: (item as any)?.entries?.length || 0,
      entries: (item as any)?.entries,
    })),
    showOverlay: false, // Always false - no more preparing overlay
  });

  return (
    <Card
      className={`bg-white dark:bg-card border border-gray-200 dark:border-border shadow-lg hover:shadow-xl transition-all duration-200 h-fit py-0 relative z-40 ${
        !section.isUnlocked ? "opacity-60 bg-gray-50 dark:bg-gray-800" : ""
      }`}
    >
      <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
        <CollapsibleTrigger asChild>
          <CardHeader
            className={`cursor-pointer hover:bg-gray-50 dark:hover:bg-background transition-colors pb-2 px-4 py-2 my-2`}
          >
            <div className="flex items-center justify-between">
              <CardTitle
                className={`text-lg font-semibold text-gray-900 dark:text-gray-100`}
              >
                {section.title}
              </CardTitle>
              <ChevronUp
                className={`h-5 w-5 text-gray-500 dark:text-gray-400 transition-transform ${
                  isExpanded ? "rotate-0" : "rotate-180"
                }`}
              />
            </div>
          </CardHeader>
        </CollapsibleTrigger>
        <CollapsibleContent>
          <CardContent className="py-0 px-3 relative">
            {section.items.map((item: any) => (
              <ItemRow
                key={item.id}
                item={{ ...item, sectionId: section.id } as any}
                disabled={!item.isUnlocked}
                lockedDeps={item.lockedDependencies}
                subtitle={item.subtitle}
                onItemClick={onItemClick}
              />
            ))}
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
};

interface BusinessSectionsGridProps {
  onItemClick: (item: BusinessItem) => void;
  projectId: string;
  stage: string;
  allTopics?: ProjectTopicSummary[];
  entriesByTopic?: Record<string, TopicEntry[]>;
}

export function BusinessSectionsGrid({
  onItemClick,
  projectId,
  stage,
  allTopics = [],
  entriesByTopic,
}: BusinessSectionsGridProps) {
  // Subscribe to the store to get live sections with entries
  const { sections } = useBusinessSectionStore();

  // Get entries getter from store
  const { getTopicEntries } = useBusinessSectionStore();

  // Create enriched sections with topics as items and their entries
  const enrichedSections: (BusinessSection & { isUnlocked: boolean })[] = [];

  const getMergedEntries = (
    sectionKey: string,
    numericTopicId: number,
    topicId: string
  ): TopicEntry[] => {
    const storeEntries = getTopicEntries(
      sectionKey,
      String(numericTopicId)
    ) as unknown as TopicEntry[];
    const backendEntries = entriesByTopic?.[topicId] || [];
    if (!backendEntries.length && !storeEntries.length) return [];
    // Merge by id, prefer store version when duplicate
    const map = new Map<string, TopicEntry>();
    backendEntries.forEach((e) => map.set(String(e.id), e));
    storeEntries.forEach((e) => map.set(String(e.id), e));
    return Array.from(map.values());
  };

  // Build a quick lookup from layer -> filled
  const layerFilledMap = useMemo(() => {
    const map = new Map<string, boolean>();
    allTopics.forEach((t) => {
      const sectionKey = t.mappedCategoryId ?? t.sectionId;
      const entries = getMergedEntries(sectionKey, t.numericTopicId, t.topicId);
      const hasEntries = Array.isArray(entries) && entries.length > 0;
      const anyConfirmed =
        hasEntries && entries.some((e: any) => e.status === "confirmed");
      const filled = Boolean(anyConfirmed);
      if (t.layer) map.set(t.layer, filled);
    });
    return map;
  }, [allTopics, getTopicEntries, sections, entriesByTopic]);

  sections.forEach((section) => {
    const sectionTopics = allTopics.filter(
      (t) => (t.mappedCategoryId ?? t.sectionId) === section.id
    );

    const topicItems = sectionTopics.map((t) => {
      const sectionKey = t.mappedCategoryId ?? t.sectionId;
      const entries = getMergedEntries(sectionKey, t.numericTopicId, t.topicId);

      // Calculate badge counts from entries
      const ideaCount = entries.filter((e) => e.status === "idea").length;
      const actionCount = entries.filter((e) => e.status === "action").length;
      const resultCount = entries.filter(
        (e) => e.status === "confirmed"
      ).length;

      // Determine topic status based on entries
      let topicStatus: "idea" | "action" | "confirmed" | "unproven" =
        "unproven";
      if (entries.length === 0) {
        topicStatus = "unproven";
      } else if (entries.every((e) => e.status === "confirmed")) {
        topicStatus = "confirmed";
      } else if (entries.some((e) => e.status === "action")) {
        topicStatus = "action";
      } else if (entries.some((e) => e.status === "idea")) {
        topicStatus = "idea";
      }

      // Dependency-based unlocking: all direct deps must be filled
      const deps = Array.isArray(t.dependencies) ? t.dependencies : [];
      const missingDeps = deps.filter(
        (dep) => layerFilledMap.get(dep) !== true
      );
      const isUnlocked = deps.length === 0 ? true : missingDeps.length === 0;

      const lockedDependencyLabels = !isUnlocked
        ? missingDeps.map((dep) => {
            const idx = deps.indexOf(dep);
            return (
              (t.dependencyLabels && idx >= 0
                ? t.dependencyLabels[idx]
                : undefined) || dep
            );
          })
        : undefined;

      return {
        id: String(t.numericTopicId),
        title: t.mappedLabel || t.title,
        status: topicStatus,
        actions: actionCount,
        ideas: ideaCount,
        results: resultCount,
        icon: getTopicIcon(t.mappedLabel || t.title),
        entries,
        isUnlocked,
        lockedDependencies: lockedDependencyLabels,
        subtitle: t.description ?? t.titlePrompt ?? undefined,
      } as unknown as BusinessItem & { entries: any[] } & {
        isUnlocked: boolean;
        lockedDependencies?: string[];
        subtitle?: string;
      };
    });

    enrichedSections.push({
      ...section,
      items: topicItems as unknown as BusinessItem[],
      isUnlocked: true, // Sections are always visible; individual topics enforce locking
    });
  });

  console.log("🔥 [BusinessSectionsGrid] RENDER:", {
    projectId,
    stage,
    sectionsCount: enrichedSections?.length || 0,
    allTopicsCount: allTopics.length,
    sectionsType: typeof enrichedSections,
    sectionsIsArray: Array.isArray(enrichedSections),
    enrichedSections: enrichedSections?.map((s) => ({
      id: s.id,
      title: s.title,
      itemsCount: s.items?.length || 0,
      items: s.items?.map((item) => ({
        id: item.id,
        title: item.title,
        hasEntries: Array.isArray((item as any)?.entries),
        entriesCount: (item as any)?.entries?.length || 0,
      })),
    })),
  });

  return (
    <div className="w-full relative z-40">
      <div className="mb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 auto-rows-min">
          {enrichedSections.map((section) => (
            <ExpandableCard
              key={section.id}
              section={section}
              onItemClick={onItemClick}
              projectId={projectId}
              stage={stage}
            />
          ))}
        </div>
      </div>
    </div>
  );
}

"use client";

import { Footer } from "./footer";
import { Header } from "./header";

interface MainLayoutProps {
  children: React.ReactNode;
  showHeader?: boolean;
  showFooter?: boolean;
  constrainHeight?: boolean; // New prop to constrain to viewport height
}

export function MainLayout({
  children,
  showHeader = true,
  showFooter = true,
  constrainHeight = false,
}: MainLayoutProps) {
  return (
    <div
      className={`${
        constrainHeight ? "h-screen" : "min-h-screen"
      } flex flex-col bg-background`}
    >
      {showHeader && <Header />}
      <main className={`flex-1 ${constrainHeight ? "min-h-0" : ""}`}>
        {children}
      </main>
      {showFooter && <Footer />}
    </div>
  );
}

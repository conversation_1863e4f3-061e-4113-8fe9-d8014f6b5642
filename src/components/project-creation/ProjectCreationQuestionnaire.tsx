"use client";

import { Logo } from "@/components/ui/logo";
import { Stepper, StepperConfig } from "@/components/ui/stepper";
import { useAiIntake } from "@/hooks/useAiIntake";
import { authApi } from "@/lib/api";
import { useProjectCreationStore } from "@/stores/projectCreationStore";
import { motion } from "framer-motion";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export function ProjectCreationQuestionnaire() {
  const {
    ideaText,
    questionnaireData,
    setQuestionnaireAnswer,
    completeQuestionnaire,
    createdProject,
    setCreatedProject,
    projectCreationStartTime,
    setProjectCreationStartTime,
    projectCreationStarted,
    setProjectCreationStarted,
  } = useProjectCreationStore();

  const [isCompleted, setIsCompleted] = useState(false);
  const [, setIsCreating] = useState(false);
  const router = useRouter();

  // AI intake (SSE) hook
  const { startIntake } = useAiIntake();

  // Trigger AI intake when questionnaire completes (after creating project)

  // Remove mock event stream; progress UI handled in project page

  // Redirect when project is created and questionnaire is completed
  useEffect(() => {
    if (isCompleted && createdProject) {
      // Project is ready, redirect to the created project
      setTimeout(() => {
        router.push(`/projects/${createdProject.id}`);
      }, 2000); // Show success for 2 seconds
    }
  }, [isCompleted, createdProject, router]);

  const stepperConfig: StepperConfig = {
    questions: [
      {
        id: "isFirstTimeFounder",
        title: "Are you a first-time founder?",
        subtitle: "This helps us tune the tone of our agents",
        type: "single",
        options: [
          { value: true, label: "Yes" },
          { value: false, label: "No" },
        ],
      },
      {
        id: "stage",
        title: "What stage are you at?",
        subtitle: "Understanding your current progress",
        type: "single",
        options: [
          { value: "idea", label: "Just an Idea" },
          { value: "building", label: "Building" },
          { value: "launched", label: "Launched & Testing" },
          { value: "revenue", label: "Generating Revenue" },
        ],
      },
      {
        id: "timeWorking",
        title: "How long have you been working on this?",
        subtitle: "This helps us understand your timeline",
        type: "single",
        options: [
          { value: "<1day", label: "Less than 1 day" },
          { value: "1-7days", label: "1-7 days" },
          { value: "7-30days", label: "7-30 days" },
          { value: "30-90days", label: "30-90 days" },
          { value: "90+days", label: "90+ days" },
        ],
      },
      {
        id: "projectName",
        title: "What would you like to name your project?",
        subtitle: "Choose a name that represents your vision",
        type: "text",
        placeholder: "Enter your project name...",
      },
    ],
    onComplete: async (answers: Record<string, any>) => {
      // Update questionnaire data in store
      Object.entries(answers).forEach(([key, value]) => {
        setQuestionnaireAnswer(key as any, value);
      });

      // Check timing to determine behavior
      const completionTime = Date.now();
      const timeTaken = projectCreationStartTime
        ? completionTime - projectCreationStartTime
        : 0;

      console.log(`Questionnaire completed in ${timeTaken}ms`);

      try {
        setIsCreating(true);
        console.log("🚀 Creating project directly...");

        const projectName =
          answers.projectName || questionnaireData.projectName || "My Project";

        // Create project directly without simulation
        const project = await authApi.createProject({
          name: projectName,
          description: ideaText || "",
        });

        console.log("✅ Project created:", project);

        const projId = (project as any).id || (project as any).projectId;

        // If the project name from the response doesn't match what we sent, update it
        const returnedName = (project as any).name;
        if (returnedName !== projectName && projId) {
          console.log("🔄 Updating project name via PATCH...");
          try {
            const updatedProject = await authApi.patchProject(String(projId), {
              name: projectName,
            });
            console.log("✅ Project name updated:", updatedProject);
          } catch (patchError) {
            console.error("Failed to update project name:", patchError);
          }
        }

        // Set the created project with the correct name
        const realProject = {
          id: projId || `${Date.now()}`,
          name: projectName, // Use the name from questionnaire
          description: (project as any).description || ideaText || "",
        };

        setCreatedProject(realProject);

        // Kick off AI intake with the initial description as message
        if (projId) {
          void startIntake(String(projId), ideaText || "");
        }

        // Wait a moment to ensure all operations complete
        await new Promise((resolve) => setTimeout(resolve, 500));

        // Navigate directly to the project
        router.push(`/projects/${projId}`);
      } catch (e) {
        console.error("Failed to create project:", e);
        setIsCompleted(true);
      } finally {
        setIsCreating(false);
      }

      completeQuestionnaire();
    },
    onStepChange: (_currentStep: number, answers: Record<string, any>) => {
      // Update answers in store as user progresses
      Object.entries(answers).forEach(([key, value]) => {
        setQuestionnaireAnswer(key as any, value);
      });

      // When project name is entered, mark creation started (no mock start)
      if (
        answers.projectName &&
        answers.projectName.trim() &&
        !projectCreationStarted
      ) {
        console.log(
          "Starting project creation with name:",
          answers.projectName
        );
        const startTime = Date.now();
        setProjectCreationStartTime(startTime);
        setProjectCreationStarted(true);
      }
    },
  };

  // Show project creation animation if completed but project not ready
  if (isCompleted && !createdProject) {
    return (
      <div className="fixed inset-0 z-50 bg-background overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-gradient-to-br from-background via-background to-muted/20" />

        {/* Noise Texture Overlay */}
        <div
          className="absolute inset-0 opacity-[0.02] dark:opacity-[0.04]"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.8' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E")`,
            backgroundSize: "256px 256px",
          }}
        />

        {/* Content */}
        <div className="relative z-10 flex items-center justify-center min-h-screen">
          <div className="text-center">
            {/* App Logo */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
              className="mb-8"
            >
              <Logo
                size={64}
                animated={true}
                showText={false}
                className="mx-auto"
              />
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="space-y-4"
            >
              <h2 className="text-2xl font-bold text-[#166534]">
                Creating your project...
              </h2>
              <p className="text-muted-foreground">
                We're setting up everything for you
              </p>
            </motion.div>
          </div>
        </div>
      </div>
    );
  }

  // Show success message when project is ready
  if (isCompleted && createdProject) {
    return (
      <div className="fixed inset-0 z-50 bg-background overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-gradient-to-br from-background via-background to-muted/20" />

        {/* Noise Texture Overlay */}
        <div
          className="absolute inset-0 opacity-[0.02] dark:opacity-[0.04]"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.8' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E")`,
            backgroundSize: "256px 256px",
          }}
        />

        {/* Content */}
        <div className="relative z-10 flex items-center justify-center min-h-screen">
          <div className="text-center">
            {/* Success Icon */}
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{
                type: "spring",
                stiffness: 200,
                damping: 20,
              }}
              className="w-16 h-16 mx-auto mb-6 rounded-full bg-[#166534] flex items-center justify-center"
            >
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.2 }}
                className="text-white text-2xl"
              >
                ✓
              </motion.div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="space-y-4"
            >
              <h2 className="text-2xl font-bold text-[#166534]">
                Your project is ready!
              </h2>
              <p className="text-muted-foreground">
                {createdProject.name} has been created successfully
              </p>
              <p className="text-sm text-muted-foreground">
                Redirecting to dashboard...
              </p>
            </motion.div>
          </div>
        </div>
      </div>
    );
  }

  return <Stepper config={stepperConfig} isLoading={isCreating} />;
}

"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Logo } from "@/components/ui/logo";
import type {
  ProjectTopicSummary,
  TopicEntry,
} from "@/hooks/queries/useTopics";
import { AnimatePresence, motion } from "framer-motion";
import { BusinessItemTable } from "../business-item-table";
import { BusinessSectionsGrid } from "../business-sections/BusinessSectionsGrid";
import { ContentSections } from "./ContentSections";

interface ProjectMainContentProps {
  activeContent: "drafts" | "files" | null;
  setActiveContent: (content: "drafts" | "files" | null) => void;
  mockDraftItems: any[]; // deprecated
  mockFileItems: any[]; // deprecated
  selectedItem: any;
  itemDetails: any[];
  sections: any[];
  isLoading: boolean;
  error: string | null;
  onBusinessItemClick: (item: any) => void;
  onBackToItems: () => void;
  projectId?: string;
  stage?: string;
  allTopics?: ProjectTopicSummary[];
  entriesByTopic?: Record<string, TopicEntry[]>;
  isDependencyLoading?: boolean;
}

export function ProjectMainContent({
  activeContent,
  setActiveContent,
  selectedItem,
  itemDetails,
  sections,
  isLoading,
  error,
  onBusinessItemClick,
  onBackToItems,
  projectId,
  stage,
  allTopics,
  entriesByTopic,
  isDependencyLoading,
}: ProjectMainContentProps) {
  return (
    <div className="flex-1 min-w-0 min-h-0 w-full max-w-full p-0 pb-20 overflow-y-auto bg-gray-50 dark:bg-gray-900 relative transition-all duration-300 ease-in-out main-content-bottom-fade">
      {/* Background layer with fade effect */}
      <div className="absolute inset-0 project-main-content pointer-events-none" />

      {/* Content layer */}
      <div className="relative z-30">
        {/* Content Section - Drafts and Files */}
        {/* Drafts/Files temporarily hidden until backed by real data */}
        <ContentSections
          activeContent={activeContent}
          setActiveContent={setActiveContent}
          mockDraftItems={[]}
          mockFileItems={[]}
        />

        <AnimatePresence mode="wait">
          {/* Business Sections Grid - Default View */}
          {!activeContent && !selectedItem && (
            <motion.div
              key="business-sections"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
            >
              {/* Loading State */}
              {isLoading && (
                <div className="flex items-center justify-center min-h-[60vh]">
                  <div className="text-center">
                    <Logo size={64} animated={true} />
                    <p className="text-gray-600 dark:text-gray-400 mt-4 text-sm">
                      Loading business sections...
                    </p>
                  </div>
                </div>
              )}

              {/* Error State */}
              {error && (
                <div className="flex items-center justify-center h-64">
                  <div className="text-center">
                    <p className="text-red-600 mb-4">{error}</p>
                    <Button onClick={() => window.location.reload()}>
                      Try Again
                    </Button>
                  </div>
                </div>
              )}

              {/* Business Sections Grid */}
              {!isLoading &&
                !error &&
                !isDependencyLoading &&
                sections.length > 0 && (
                  <BusinessSectionsGrid
                    onItemClick={onBusinessItemClick}
                    projectId={projectId as string}
                    stage={stage as string}
                    allTopics={allTopics}
                    entriesByTopic={entriesByTopic}
                  />
                )}

              {/* Empty State */}
              {!isLoading && !error && sections.length === 0 && (
                <div className="flex items-center justify-center h-64">
                  <div className="text-center">
                    <p className="text-gray-600 mb-4">
                      No business sections found
                    </p>
                    <Button onClick={() => window.location.reload()}>
                      Reload
                    </Button>
                  </div>
                </div>
              )}

              {/* Topics list below sections */}
              {/* TopicsSection removed; topics now render inside each section */}
            </motion.div>
          )}

          {/* Business Item Detail View */}
          {!activeContent && selectedItem && (
            <motion.div
              key="business-detail"
              initial={{ opacity: 0, x: 30 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -30 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
            >
              <BusinessItemTable
                itemDetails={itemDetails}
                selectedBusinessItem={selectedItem}
                onBackToItems={onBackToItems}
                sectionId={(selectedItem as any)?.sectionId}
                topicId={selectedItem?.id}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
}

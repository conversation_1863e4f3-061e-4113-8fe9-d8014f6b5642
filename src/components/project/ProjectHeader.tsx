"use client";

import { useSidebar } from "@/components/ui/sidebar";
import { SidebarButton } from "@/components/ui/sidebar-button";
import { useAnalytics } from "@/hooks/useAnalytics";
import { ICON_SIZES } from "@/lib/constants";
import { FileText, Home } from "lucide-react";
import { useRouter } from "next/navigation";
import { PrioritiesDropdown } from "./PrioritiesDropdown";
import { ProgressBar } from "./ProgressBar";
import { ProjectDetailHeader } from "./ProjectDetailHeader";

interface ProjectHeaderProps {
  activeContent: "drafts" | "files" | null;
  setActiveContent: (content: "drafts" | "files" | null) => void;
  selectedBusinessItem: any;
  onBackToItems: () => void;
  progress?: number;
  projectTitle?: string;
  projectName?: string;
}

export function ProjectHeader({
  activeContent,
  setActiveContent,
  selectedBusinessItem,
  onBackToItems,
  progress = 0,
  projectTitle: _projectTitle,
  projectName,
}: ProjectHeaderProps) {
  const router = useRouter();
  const { trackClick, trackCustomEvent } = useAnalytics();
  const { state, isMobile } = useSidebar();

  // If an item is selected, show the detail header
  if (selectedBusinessItem) {
    console.log(
      "[ProjectHeader] showing detail header for item",
      selectedBusinessItem?.id
    );
    return (
      <ProjectDetailHeader
        selectedBusinessItem={selectedBusinessItem}
        onBackToItems={onBackToItems}
        projectName={projectName}
      />
    );
  }

  return (
    <header className="flex flex-col h-20 shrink-0 transition-[width] ease-linear border-b border-border">
      <div
        className={`flex items-center w-full flex-1 px-4 ${
          isMobile ? "justify-between" : "gap-4"
        }`}
      >
        {/* Mobile: Home button */}
        {isMobile && (
          <SidebarButton
            onClick={() => {
              trackClick("home-button", "project-header");
              trackCustomEvent("navigation_clicked", {
                destination: "dashboard",
                from_page: "project-detail",
                location: "header",
              });
              router.push("/user-dashboard");
            }}
            icon={Home}
            variant="ghost"
            size="lg"
            layout="icon-only"
            showBorder={true}
            hoverColor="grey"
            hoverScale={true}
            iconClassName={ICON_SIZES.lg}
          />
        )}

        {/* Title, progress bar and right items container */}
        <div
          className={`flex items-center ${isMobile ? "gap-2" : "gap-4"} ${
            isMobile ? "" : "flex-1"
          }`}
        >
          {/* Project title intentionally removed for cleaner header */}
          {/* Progress bar */}
          <div className={isMobile ? "w-12" : "flex-1"}>
            <ProgressBar
              progress={Math.max(0, Math.min(100, Math.round(progress)))}
            />
          </div>

          {/* Right side - Priorities, Drafts, and Profile */}
          <div className="flex items-center gap-2 h-full">
            {/* Priorities dropdown - moved from sidebar */}
            <PrioritiesDropdown />

            {/* Drafts button */}
            <SidebarButton
              onClick={() => {
                trackClick("drafts-button", "project-header");
                trackCustomEvent("content_section_opened", {
                  section: "drafts",
                  location: "project-header",
                });
                setActiveContent("drafts");
              }}
              icon={FileText}
              variant="ghost"
              size="lg"
              layout="icon-only"
              showBorder={true}
              hoverColor="grey"
              hoverScale={true}
              iconClassName={ICON_SIZES.lg}
            />

            {/* User profile button removed as requested */}
          </div>
        </div>
      </div>
    </header>
  );
}

"use client";

import { AiIntakeProgress } from "@/components/ai/AiIntakeProgress";
import { Button } from "@/components/ui/button";
import { useSidebar } from "@/components/ui/sidebar";
import { useAiChat } from "@/hooks/useAiChat";
import { useAiIntake } from "@/hooks/useAiIntake";
import { safeLocalStorage } from "@/lib/storage";
import { useChatStore } from "@/stores/chatStore";
import type { ChatMessage } from "@/types/Chat.types";
import { useEffect, useState } from "react";
import { AI_Prompt } from "./ui/animated-ai-input";
import { ChatBubble, ChatBubbleMessage } from "./ui/chat-bubble";
import { ChatMessageList } from "./ui/chat-message-list";
import { Logo } from "./ui/logo";

interface ProjectChatSidebarProps {
  projectId: string;
  isCollapsed?: boolean;
  onCollapseChange?: (collapsed: boolean) => void;
  embedded?: boolean; // New prop to indicate if it's embedded in sidebar
  chatWidth?: "45%" | "45%";
  setChatWidth?: (width: "45%" | "45%") => void;
  isChatCollapsed?: boolean;
  setIsChatCollapsed?: (collapsed: boolean) => void;
  selectedBusinessItem?: any;
}

// Welcome message based on context
const getWelcomeMessage = (
  isDetailsPage: boolean = false,
  topicName?: string
): ChatMessage => ({
  id: "welcome",
  user: "Siift AI",
  avatar: "",
  message:
    isDetailsPage && topicName
      ? `So far, we have a few ideas for ${topicName} in the focus table on the right - let's validate them with by setting some actions and then recording the results!`
      : "Ok we've filled in a few key topics, click on one of them to dive in!",
  timestamp: new Date().toLocaleTimeString([], {
    hour: "2-digit",
    minute: "2-digit",
  }),
  isCurrentUser: false,
});

export function ProjectChatSidebar({
  projectId,
  embedded = false,
  selectedBusinessItem,
}: ProjectChatSidebarProps) {
  const [internalIsCollapsed, setInternalIsCollapsed] = useState(false);
  const [internalChatWidth, setInternalChatWidth] = useState<"45%" | "45%">(
    "45%"
  );
  const { state } = useSidebar();

  // Chat store integration
  const { messages, setMessages } = useChatStore();

  // AI chat hook for streaming
  const { sendMessage, cancelMessage, isStreaming } = useAiChat(projectId);
  const { stage } = useAiIntake();
  const [showIntakeProgress, setShowIntakeProgress] = useState(false);

  // Auto-show intake progress when stage is active
  useEffect(() => {
    const isIntakeActive = stage && !["idle", "ready"].includes(stage);
    setShowIntakeProgress(isIntakeActive);
  }, [stage]);

  // Initialize messages on mount (do not fake session ID)
  useEffect(() => {
    if (!projectId) return;
    const savedMessages = safeLocalStorage.getJSON<ChatMessage[]>(
      `chat_messages_${projectId}`,
      []
    );
    if (savedMessages.length > 0) {
      setMessages(savedMessages);
    } else {
      // Pass true if we have a selectedBusinessItem (details page) and the topic name
      setMessages([
        getWelcomeMessage(!!selectedBusinessItem, selectedBusinessItem?.title),
      ]);
    }
  }, [projectId, setMessages, selectedBusinessItem]);

  // Handle sending messages - now using the useAiChat hook
  const handleSendMessage = sendMessage;

  // Use external state if provided, otherwise use internal state

  // Chat is always expanded - no collapse functionality

  // Render embedded version for sidebar
  if (embedded) {
    // Don't show anything when sidebar is collapsed
    if (state === "collapsed") {
      return null;
    }

    // Always show expanded chat - no collapse functionality
    return (
      <div
        className={`w-full h-full border-t border-border ${
          selectedBusinessItem
            ? "bg-[var(--siift-lightest-accent)]/20 dark:bg-[color:var(--siift-dark-accent)]/20"
            : "bg-[var(--siift-light-main)]/20 dark:bg-[color:var(--siift-darker)]/40"
        } backdrop-blur-sm flex flex-col transition-all duration-300`}
      >
        {/* AI Intake Progress - dev only */}
        {process.env.NODE_ENV === "development" && (
          <div className="p-3 border-b border-border/50">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">AI Processing</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowIntakeProgress(!showIntakeProgress)}
                className="h-6 text-xs"
              >
                {showIntakeProgress ? "Hide" : "Show"}
              </Button>
            </div>
            {showIntakeProgress && <AiIntakeProgress projectId={projectId} />}
          </div>
        )}

        {/* Messages */}
        <div className="flex-1 overflow-hidden w-full min-h-0 relative">
          <ChatMessageList className="w-full h-full">
            {messages.map((msg) => (
              <ChatBubble
                key={msg.id}
                className="mt-2"
                variant={msg.isCurrentUser ? "sent" : "received"}
              >
                {!msg.isCurrentUser && (
                  <div className="h-8 w-8 shrink-0 bg-primary/10 rounded-lg flex items-center justify-center">
                    <Logo size={20} />
                  </div>
                )}
                <ChatBubbleMessage
                  variant={msg.isCurrentUser ? "sent" : "received"}
                >
                  <div className="text-md leading-relaxed whitespace-pre-line">
                    {msg.message}
                  </div>
                  {!msg.isCurrentUser && msg.cta?.type === "refetch_topics" && (
                    <div className="mt-2">
                      <Button
                        size="sm"
                        className="h-7"
                        onClick={async () => {
                          // Aggressively invalidate and refetch topics and entries using our shared client
                          const { queryClient } = await import(
                            "@/lib/queryClient"
                          );
                          const keys: any[] = [
                            ["all-project-topics-v2", projectId],
                            ["all-topic-entries", projectId],
                            ["topics", projectId],
                            ["business-sections", projectId],
                          ];
                          keys.forEach((k) =>
                            queryClient.invalidateQueries({ queryKey: k })
                          );
                          keys.forEach((k) =>
                            queryClient.refetchQueries({ queryKey: k })
                          );
                        }}
                      >
                        {msg.cta.label || "Start Siifting"}
                      </Button>
                    </div>
                  )}
                </ChatBubbleMessage>
              </ChatBubble>
            ))}
          </ChatMessageList>
        </div>

        {/* Message Input */}
        <div className="p-3 border-t border-border/50 bg-muted/30 backdrop-blur-sm w-full flex-shrink-0">
          <AI_Prompt
            onSendMessage={handleSendMessage}
            onStop={() => cancelMessage()}
            isLoading={isStreaming}
            placeholder={
              isStreaming
                ? "AI is responding..."
                : selectedBusinessItem
                ? `Ask me about ${selectedBusinessItem.title}...`
                : "Ask me anything about your project..."
            }
          />

          {/* Disclaimer */}
          <div className="mt-1 px-2">
            <p className="text-[10px] text-center opacity-50 leading-tight">
              Siift can make mistakes. Please double check answers to ensure
              accuracy.
            </p>
          </div>
        </div>
      </div>
    );
  }
}
